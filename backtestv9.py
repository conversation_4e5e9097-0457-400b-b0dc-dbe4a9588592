# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
import os
import sys
import re
from datetime import datetime, time
from tabulate import tabulate
import warnings
import sqlite3

# 导入概念和板块过滤器
from concept_sector_filter import filter_meaningful_concepts_and_sectors, get_meaningless_items, is_meaningful_concept

# 注：已移除股票价格缓存模块导入，因为不再需要买入功能

# --- 1. 配置区域 ---
# 数据根目录配置 - 请修改为您自己的数据文件夹路径
BASE_DATA_DIR = r'D:\dev\mootdx\adata\Gemini\fund_data'
# 回测日期配置 - 请修改为您想要复盘的日期
BACKTEST_DATE = '2025-08-04'

# 数据库路径配置 (用于获取股票-板块映射关系)
STOCK_BLOCK_DB_PATH = r'D:\dev\mootdx\stock_block_analysis.db'
STOCK_BLOCK_DB_FALLBACK = 'stock_block_analysis.db'

# 分析参数配置
MIN_SECTORS_FOR_ANALYSIS = 4  # 板块/概念排行榜至少需要多少条数据才能进行分析
ANALYSIS_TOP_N = 10  # 板块/概念断层分析时，关注前N名

# ==================== 分析配置参数 ====================
# 注：已移除所有买入信号相关配置，仅保留分析功能

# 调试和显示控制开关
DEBUG_LOG_ENABLED = False  # 是否显示[DEBUG]日志，默认关闭
SHOW_INTERNAL_ANALYSIS = False  # 是否显示内部个股分析日志，默认关闭

# 重点监控行业配置
MONITORED_INDUSTRIES = ['首扳最多行业', '2板连板数最多行业']  # 需要特别监控的行业类型

# ====================================================

# --- 配置区域结束 ---

warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)
pd.set_option('mode.chained_assignment', None)


# ==============================================================================
# § 0.1 调试日志工具函数
# ==============================================================================

def debug_print(message):
    """调试日志打印函数，根据DEBUG_LOG_ENABLED控制是否输出"""
    if DEBUG_LOG_ENABLED:
        print(message)


# ==============================================================================
# § 0. 日志管理类
# ==============================================================================

class LogCapture:
    """日志捕获类，用于将print输出同时保存到文件"""
    def __init__(self, log_file_path, date_str=None):
        self.log_file_path = log_file_path
        self.date_str = date_str
        self.original_stdout = sys.stdout
        self.log_file = None

    def __enter__(self):
        # 首次打开时使用覆盖模式清空文件，然后切换到追加模式
        with open(self.log_file_path, 'w', encoding='utf-8') as f:
            f.write(f"=== BacktestV9 分析日志 - {self.date_str} ===\n")
            f.write("分析版本：V9.0 - 资金流分析专用版（无买入功能）\n")
            f.write("=" * 60 + "\n\n")

        # 然后以追加模式打开用于后续写入
        self.log_file = open(self.log_file_path, 'a', encoding='utf-8')
        sys.stdout = self
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stdout = self.original_stdout
        if self.log_file:
            self.log_file.close()

    def write(self, text):
        # 同时写入控制台和文件
        self.original_stdout.write(text)
        if self.log_file:
            self.log_file.write(text)
            self.log_file.flush()

    def flush(self):
        self.original_stdout.flush()
        if self.log_file:
            self.log_file.flush()


# ==============================================================================
# § 1. 涨停判断工具函数
# ==============================================================================

def is_stock_limit_up(stock_code, current_price, change_percent):
    """
    判断股票是否已经涨停

    Args:
        stock_code: 股票代码
        current_price: 当前价格
        change_percent: 当前涨跌幅(%)

    Returns:
        bool: True表示已涨停，False表示未涨停
    """
    try:
        # 确保股票代码是字符串格式
        stock_code = str(stock_code).zfill(6)

        # 根据股票代码判断涨停幅度
        if stock_code.startswith(('300', '688')):
            # 创业板(300)和科创板(688)：20%涨停
            limit_up_threshold = 19.9
        elif stock_code.startswith(('000', '001', '002', '600', '601', '603', '605')):
            # 主板：10%涨停
            limit_up_threshold = 9.9
        else:
            # 其他情况默认10%
            limit_up_threshold = 9.9

        # 判断是否接近或达到涨停
        if change_percent >= limit_up_threshold:
            return True

        return False

    except Exception as e:
        print(f"涨停判断失败 {stock_code}: {e}")
        return False


# ==============================================================================
# § 2. 分析引擎类（已移除买入功能，仅保留分析功能）
# ==============================================================================

# 注：原回测引擎类已完全移除，不再包含任何买入、持仓、交易相关功能


# ==============================================================================
# § A. 核心辅助函数 (从 dynamic_gap_detector.py 移植并优化)
# ==============================================================================

def convert_to_float(value):
    """将包含'亿'或'万'的字符串转换为浮点数"""
    if isinstance(value, (int, float)):
        return float(value)
    if isinstance(value, str):
        try:
            value = value.replace('%', '').replace(',', '')
            if '亿' in value:
                return float(value.replace('亿', '')) * 1e8
            elif '万' in value:
                return float(value.replace('万', '')) * 1e4
            return float(value)
        except (ValueError, TypeError):
            return 0.0
    return 0.0


def format_amount(amount):
    """将数字格式化为易读的中文金额字符串"""
    if pd.isna(amount): return 'N/A'
    amount = float(amount)
    if abs(amount) >= 1e8: return f"{amount / 1e8:.2f}亿"
    if abs(amount) >= 1e4: return f"{amount / 1e4:.2f}万"
    return f"{amount:.2f}"


def extract_timestamp_from_filename(filename):
    """从多种格式的文件名中提取时间戳 (HHMMSS)"""
    patterns = [
        r'_\d{8}_(\d{6})\.',  # e.g., fund_flow_rank_20250725_093047.csv
        r'_(\d{6})\.csv$',  # e.g., zt_pool_akshare_东方财富_093651.csv
        r'^(\d{2})-(\d{2})_'  # e.g., 09-30_zt_pool.csv -> HHMM00
    ]
    for pattern in patterns:
        match = re.search(pattern, filename)
        if match:
            if len(match.groups()) == 2:  # 匹配 HH-MM_ 格式
                return f"{match.group(1)}{match.group(2)}00"
            return match.group(1)
    return None


def find_latest_file(file_list, current_time):
    """在文件列表中查找不晚于当前模拟时间的最新文件"""
    latest_file = None
    latest_timestamp = None
    for f in sorted(file_list):
        ts_str = extract_timestamp_from_filename(f)
        if ts_str:
            try:
                file_time = datetime.strptime(ts_str, '%H%M%S').time()
                if file_time <= current_time:
                    if latest_timestamp is None or file_time > latest_timestamp:
                        latest_timestamp = file_time
                        latest_file = f
                else:
                    # 由于文件是排序的，一旦超过当前时间，后续的都会超过
                    break
            except ValueError:
                continue
    return latest_file


def find_latest_file_from_manifest(file_manifest, file_type, current_time, sub_key=None):
    """从文件索引清单中查找不晚于当前模拟时间的最新文件（优化版）"""
    latest_file = None
    latest_timestamp = None

    # 遍历所有时间戳，找到不晚于当前时间的最新文件
    for ts_str, files_dict in file_manifest.items():
        try:
            # 确保时间戳格式正确
            if not ts_str or len(ts_str) != 6:
                continue

            file_time = datetime.strptime(ts_str, '%H%M%S').time()
            if file_time <= current_time:
                # 检查该时间点是否有对应类型的文件
                if file_type in files_dict:
                    file_data = files_dict[file_type]

                    # 处理嵌套字典（如concept_summary, sector_summary）
                    if sub_key and isinstance(file_data, dict):
                        if sub_key in file_data:
                            if latest_timestamp is None or file_time > latest_timestamp:
                                latest_timestamp = file_time
                                latest_file = file_data[sub_key]
                    elif not sub_key and isinstance(file_data, str):
                        # 直接文件名
                        if latest_timestamp is None or file_time > latest_timestamp:
                            latest_timestamp = file_time
                            latest_file = file_data
        except (ValueError, TypeError) as e:
            debug_print(f"[DEBUG] 跳过无效时间戳: {ts_str}, 错误: {e}")
            continue

    return latest_file


def get_stock_sectors(stock_name, stock_code=None, stock_sector_map=None):
    """从预加载的映射表或数据库获取股票的概念和行业信息（优化版）"""
    # 优先使用预加载的映射表
    if stock_sector_map:
        # 首先尝试通过股票代码查找
        if stock_code:
            clean_code = str(stock_code).split('.')[0]
            if clean_code in stock_sector_map:
                return stock_sector_map[clean_code]

        # 然后尝试通过股票名称查找
        if stock_name and stock_name in stock_sector_map:
            return stock_sector_map[stock_name]

        # 如果映射表中没有找到，返回空结果（避免回退到数据库查询）
        return {'concepts': [], 'industries': []}

    # 回退到原始数据库查询方式（保持向后兼容）
    db_path = STOCK_BLOCK_DB_PATH if os.path.exists(STOCK_BLOCK_DB_PATH) else STOCK_BLOCK_DB_FALLBACK
    if not os.path.exists(db_path):
        return {'concepts': [], 'industries': []}

    query = "SELECT concept_blocks, industry_blocks FROM stocks WHERE "
    params = ()
    if stock_code:
        clean_code = str(stock_code).split('.')[0]
        query += "stock_code = ?"
        params = (clean_code,)
    elif stock_name:
        query += "short_name = ?"
        params = (stock_name,)
    else:
        return {'concepts': [], 'industries': []}

    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            result = cursor.fetchone()
            if result:
                concepts = result[0].split(',') if result[0] else []
                industries = result[1].split(',') if result[1] else []

                # 【新增：过滤无意义概念和行业】
                filtered_concepts = filter_meaningful_concepts_and_sectors(concepts) if concepts else []
                filtered_industries = filter_meaningful_concepts_and_sectors(industries) if industries else []

                return {'concepts': filtered_concepts, 'industries': filtered_industries}
    except Exception as e:
        print(f"数据库查询失败 ({stock_name}): {e}")
    return {'concepts': [], 'industries': []}


# ==============================================================================
# § B. 个股资金流断层分析模块 (移植自 dynamic_gap_detector.py)
# ==============================================================================

def get_stock_filter_strategy(file_format):
    """根据文件格式确定过滤策略"""
    filter_strategies = {
        'fund_flow_rank_format': 'positive_and_gain', # 资金流排名：净流入>0且涨跌幅>=0
        'rank_format': 'positive_and_gain',   # 排名格式：净流入>0且涨跌幅>=0
        'individual_format': 'positive_only', # 个股资金流排名：只要净流入>0
        'tpdog_format': 'positive_only',      # tpdog：只要净流入>0
        'akshare_format': 'positive_only',    # akshare：只要净流入>0
        'stock_flow_format': 'positive_only', # 个股资金流通用：只要净流入>0
        'standard_format': 'positive_only'    # 标准：只要净流入>0
    }
    return filter_strategies.get(file_format, 'positive_only')

def apply_stock_filter(stock_flow_data, file_format):
    """根据文件格式应用对应的过滤策略"""
    strategy = get_stock_filter_strategy(file_format)

    # 检查必需的列是否存在
    if '今日主力净流入-净额' not in stock_flow_data.columns:
        print(f"[ERROR] 缺少必需的列 '今日主力净流入-净额'")
        debug_print(f"[DEBUG] 当前可用列: {list(stock_flow_data.columns)}")
        # 尝试查找可能的替代列名
        possible_columns = [col for col in stock_flow_data.columns if '净流入' in col or 'net_inflow' in col.lower()]
        if possible_columns:
            print(f"[INFO] 发现可能的净流入列: {possible_columns}")
            # 使用第一个找到的列
            net_inflow_col = possible_columns[0]
            print(f"[INFO] 使用列 '{net_inflow_col}' 作为净流入数据")
        else:
            print(f"[ERROR] 未找到任何净流入相关列，返回空数据")
            return stock_flow_data.iloc[0:0]  # 返回空DataFrame但保持结构
    else:
        net_inflow_col = '今日主力净流入-净额'

    if strategy == 'positive_and_gain':
        # 要求净流入>0且涨跌幅>=0
        if '今日涨跌幅' in stock_flow_data.columns:
            return stock_flow_data[
                (stock_flow_data[net_inflow_col] > 0) &
                (stock_flow_data['今日涨跌幅'] >= 0)
            ]
        else:
            # 如果没有涨跌幅数据，退化为只要求净流入>0
            return stock_flow_data[stock_flow_data[net_inflow_col] > 0]
    else:
        # 默认策略：只要求净流入>0
        return stock_flow_data[stock_flow_data[net_inflow_col] > 0]

def classify_file_type(filename):
    """根据文件名分类文件类型，支持多种格式 - 简化版"""
    filename_lower = filename.lower()
    
    # 资金流排名格式
    if ('fund_flow_rank' in filename_lower or 
        'individual_fund_flow' in filename_lower or
        '个股资金流' in filename_lower):
        return 'fund_flow_rank_format'
    
    # tpdog格式
    if 'tpdog' in filename_lower:
        return 'tpdog_format'
    
    # akshare格式
    if 'akshare' in filename_lower:
        return 'akshare_format'
    
    # 涨停池格式
    if ('limit_up' in filename_lower or 'zt_pool' in filename_lower or '涨停' in filename_lower):
        return 'individual_format'
    
    # 个股资金流通用格式
    if ('stock_flow' in filename_lower or '个股' in filename_lower):
        return 'stock_flow_format'
    
    # 默认格式
    return 'standard_format'

def apply_column_mapping(df, column_mapping):
    """应用列名映射并验证必要列"""
    mapped_columns = {}
    available_columns = []
    
    debug_print(f"[DEBUG] 原始文件列名: {list(df.columns)}")

    for target_col, possible_names in column_mapping.items():
        found = False
        for possible_name in possible_names:
            if possible_name in df.columns:
                mapped_columns[possible_name] = target_col
                available_columns.append(target_col)
                debug_print(f"[DEBUG] 映射成功: {possible_name} -> {target_col}")
                found = True
                break
        if not found:
            debug_print(f"[DEBUG] 未找到{target_col}列，尝试的列名: {possible_names}")

    # 检查必要列
    if '名称' not in available_columns:
        print(f"缺少股票名称列，可用列: {list(df.columns)}")
        return None

    # 重命名列并选择可用列
    df_clean = df.rename(columns=mapped_columns)
    df_clean = df_clean[available_columns].copy()

    # 如果没有代码列，添加空的代码列以保持兼容性
    if '代码' not in available_columns:
        print(f"[WARNING] 文件中未找到股票代码列，将添加空的代码列以保持兼容性")
        df_clean['代码'] = ''
    
    # 如果没有价格列，添加空的价格列以保持兼容性
    if '最新价' not in available_columns:
        print(f"[WARNING] 文件中未找到价格列，将添加空的价格列以保持兼容性")
        df_clean['最新价'] = 0.0

    # 如果没有净流入列，添加默认的净流入列以保持兼容性
    if '今日主力净流入-净额' not in available_columns:
        print(f"[WARNING] 文件中未找到净流入列，将添加默认净流入列以保持兼容性")
        # 根据涨跌幅生成模拟的净流入数据，涨幅越大净流入越多
        if '今日涨跌幅' in df_clean.columns:
            # 基于涨跌幅生成合理的净流入数据
            df_clean['今日主力净流入-净额'] = df_clean['今日涨跌幅'].apply(
                lambda x: max(0, float(x) * 10000000) if pd.notna(x) else 0  # 涨跌幅*1000万作为净流入
            )
        else:
            df_clean['今日主力净流入-净额'] = 50000000  # 默认5000万净流入

    # 如果没有净流入占比列，添加默认列
    if '今日主力净流入-净占比' not in available_columns:
        print(f"[WARNING] 文件中未找到净流入占比列，将添加默认列")
        df_clean['今日主力净流入-净占比'] = 5.0  # 默认5%

    debug_print(f"[DEBUG] 处理后的列名: {list(df_clean.columns)}")
    return df_clean

def parse_tpdog_format(df):
    """处理tpdog格式文件"""
    column_mapping = {
        '名称': ['名称', 'name', '股票名称'],
        '代码': ['代码', 'code', '股票代码', '证券代码', 'symbol', 'stock_code'],
        '最新价': ['最新价', 'price', '现价', 'latest_price'],
        '今日涨跌幅': ['今日涨跌幅', '涨跌幅', 'change_pct'],
        '今日主力净流入-净额': ['今日主力净流入-净额', 'main_net_inflow', '主力净流入', '净流入'],
        '今日主力净流入-净占比': ['今日主力净流入-净占比', 'r_net', '主力净流入占比', 'main_net_inflow_pct', '净占比', '占比'],
        '换手率': ['换手率', 'turnover_rate', 'turnover']
    }
    
    df_processed = apply_column_mapping(df, column_mapping)
    return df_processed

def parse_standard_format(df):
    """处理标准格式文件"""
    column_mapping = {
        '名称': ['名称', 'name', '股票名称'],
        '代码': ['代码', 'code', '股票代码', '证券代码', 'symbol', 'stock_code'],
        '最新价': ['最新价', 'price', '现价', 'latest_price'],
        '今日涨跌幅': ['今日涨跌幅', '涨跌幅', 'change_pct'],
        '今日主力净流入-净额': ['今日主力净流入-净额', 'main_net_inflow', '主力净流入', '净流入'],
        '今日主力净流入-净占比': ['今日主力净流入-净占比', 'main_net_inflow_pct', '主力净流入占比', 'r_net', '净占比', '占比'],
        '换手率': ['换手率', 'turnover_rate', 'turnover']
    }
    
    df_processed = apply_column_mapping(df, column_mapping)
    return df_processed

def standardize_stock_data(df, file_format):
    """根据文件格式标准化股票数据列名"""
    if file_format == 'tpdog_format':
        return parse_tpdog_format(df)
    else:
        return parse_standard_format(df)

# 点火检测相关常量
MIN_SECTORS_FOR_ANALYSIS = 5
SLIDING_WINDOW_MINUTES = 30
MIN_RANK_THRESHOLD = 20
MIN_PF_THRESHOLD = 1.25
WRA_MULTIPLIER = 1.5
CT_MULTIPLIER = 1.5

# 持续攻击检测常量
SUSTAINED_WINDOW_MINUTES = 60
SUSTAINED_RANK_THRESHOLD = 200
SUSTAINED_CUMULATIVE_INFLOW_RANK_BENCHMARK = 30
SUSTAINED_INFLOW_STABILITY_RATIO = 0.7
SUSTAINED_RANK_SLOPE_THRESHOLD = -0.5
SUSTAINED_MIN_DATA_POINTS = 5

class MarketPulseDataPool:
    """V3.0 市场脉搏实时数据池 - 滑动窗口管理器"""

    def __init__(self, window_minutes=30):
        self.window_minutes = window_minutes
        self.data_pool = []

    def add(self, stock_name, wra, ct, rank, timestamp):
        """添加新的数据点"""
        from datetime import datetime, timedelta
        
        if isinstance(timestamp, str):
            timestamp = datetime.strptime(timestamp, '%H:%M:%S')
        elif isinstance(timestamp, datetime):
            # 如果已经是datetime对象，直接使用
            pass
        else:
            # 如果是time对象，转换为datetime
            timestamp = datetime.combine(datetime.today(), timestamp)
        
        self.data_pool.append({
            'timestamp': timestamp,
            'stock_name': stock_name,
            'wra': wra,
            'ct': ct,
            'rank': rank
        })
        self._cleanup_old_data()

    def _cleanup_old_data(self):
        """清理过期数据"""
        from datetime import datetime, timedelta
        if not self.data_pool:
            return
        
        latest_time = max(entry['timestamp'] for entry in self.data_pool)
        cutoff_time = latest_time - timedelta(minutes=self.window_minutes)
        self.data_pool = [entry for entry in self.data_pool if entry['timestamp'] >= cutoff_time]

    def get_stock_history(self, stock_name):
        """获取特定股票的历史数据"""
        return [entry for entry in self.data_pool if entry['stock_name'] == stock_name]

class ObservationPool:
    """V5.1 持续性验证观察池 - 过滤毛刺信号"""
    
    def __init__(self, observation_minutes=15):
        from datetime import datetime, timedelta
        self.observation_minutes = observation_minutes
        self.pool = {}  # {stock_name: {'entry_time': datetime, 'data': {...}}}
        
    def add_candidate(self, stock_name, data, current_time):
        """将股票加入观察池"""
        from datetime import datetime
        if isinstance(current_time, str):
            current_time = datetime.strptime(current_time, '%H:%M:%S')
        elif isinstance(current_time, datetime):
            # 如果已经是datetime对象，直接使用
            pass
        else:
            # 如果是time对象，转换为datetime
            current_time = datetime.combine(datetime.today(), current_time)
            
        self.pool[stock_name] = {
            'entry_time': current_time,
            'data': data
        }
        self._cleanup_expired_candidates(current_time)
        
    def _cleanup_expired_candidates(self, current_time):
        """清理过期的候选股票"""
        from datetime import timedelta
        expired_stocks = []
        cutoff_time = current_time - timedelta(minutes=self.observation_minutes)
        
        for stock_name, info in self.pool.items():
            if info['entry_time'] < cutoff_time:
                expired_stocks.append(stock_name)
        
        for stock_name in expired_stocks:
            del self.pool[stock_name]

class StockFlowIgnitionDetector:
    """V7.0 个股资金流信号检测器 - "爆发点火"与"持续攻击"双模型矩阵版"""

    def __init__(self):
        # V5.1 及之前版本所需属性
        self.previous_snapshot = None
        self.market_pulse_pool = MarketPulseDataPool(SLIDING_WINDOW_MINUTES)
        self.observation_pool = ObservationPool()

        # V7.0 新增：用于"持续攻击"信号检测的长时间序列数据
        self.long_term_stock_data = {}  # {stock_name: [datapoint_1, datapoint_2, ...]}

        self.ignition_thresholds = {
            'min_rank': MIN_RANK_THRESHOLD,
            'min_pf': MIN_PF_THRESHOLD,
            'wra_multiplier': WRA_MULTIPLIER,
            'ct_multiplier': CT_MULTIPLIER,
            'min_score': 7.0
        }

        # V10.0 新增：板块映射缓存
        self.stock_board_mapping = None

    def _load_stock_board_mapping(self):
        """加载股票-板块映射关系"""
        # 简化版实现，避免复杂的文件依赖
        return None

    def detect_ignition_signals(self, current_data, current_time, market_snapshot=None):
        """V7.0 - 检测"爆发点火"与"持续攻击"两种信号"""
        if self.previous_snapshot is None:
            self.previous_snapshot = self._create_data_snapshot(current_data, current_time)
            self._update_long_term_data(current_data, current_time)
            return []

        # 检测爆发点火信号
        ignition_signals = self._detect_ignition_signals_v5(current_data, current_time, market_snapshot)
        
        # 检测持续攻击信号
        sustained_signals = self._detect_sustained_attack_signals(current_data, current_time)
        
        # 更新数据
        self.previous_snapshot = self._create_data_snapshot(current_data, current_time)
        self._update_long_term_data(current_data, current_time)
        
        # 合并信号
        all_signals = ignition_signals + sustained_signals
        return all_signals

    def _create_data_snapshot(self, current_data, current_time):
        """创建数据快照"""
        return {
            'timestamp': current_time,
            'data': current_data.copy() if not current_data.empty else None,
            'top_stock': current_data.iloc[0]['名称'] if not current_data.empty else None
        }

    def _update_long_term_data(self, current_data, current_time):
        """更新长期数据存储"""
        from datetime import datetime, timedelta
        
        # 为每只股票添加当前数据点
        for idx, row in current_data.iterrows():
            stock_name = row['名称']
            if stock_name not in self.long_term_stock_data:
                self.long_term_stock_data[stock_name] = []
            
            self.long_term_stock_data[stock_name].append({
                'timestamp': current_time,
                'rank': idx + 1,
                'inflow': row['今日主力净流入-净额'],
                'data': row.to_dict()
            })
        
        # 清理过期数据（保留60分钟）
        if isinstance(current_time, str):
            current_time = datetime.strptime(current_time, '%H:%M:%S')
        elif isinstance(current_time, datetime):
            # 如果已经是datetime对象，直接使用
            pass
        else:
            # 如果是time对象，转换为datetime
            current_time = datetime.combine(datetime.today(), current_time)
            
        cutoff_time = current_time - timedelta(minutes=SUSTAINED_WINDOW_MINUTES)
        
        for stock_name in list(self.long_term_stock_data.keys()):
            self.long_term_stock_data[stock_name] = [
                point for point in self.long_term_stock_data[stock_name]
                if isinstance(point['timestamp'], datetime) and point['timestamp'] >= cutoff_time
            ]
            
            # 如果没有数据了，删除该股票
            if not self.long_term_stock_data[stock_name]:
                del self.long_term_stock_data[stock_name]

    def _detect_ignition_signals_v5(self, current_data, current_time, market_snapshot=None):
        """检测爆发点火信号"""
        if self.previous_snapshot is None or self.previous_snapshot['data'] is None:
            return []
        
        signals = []
        previous_data = self.previous_snapshot['data']
        
        # 简化的点火检测逻辑
        for idx, row in current_data.head(20).iterrows():
            stock_name = row['名称']
            current_rank = idx + 1
            current_inflow = row['今日主力净流入-净额']
            
            # 查找在前一快照中的位置
            prev_row = previous_data[previous_data['名称'] == stock_name]
            if prev_row.empty:
                continue
                
            prev_rank = prev_row.index[0] + 1
            prev_inflow = prev_row.iloc[0]['今日主力净流入-净额']
            
            # 简单的点火条件：排名大幅提升且资金流入显著增加
            rank_improvement = prev_rank - current_rank
            inflow_ratio = current_inflow / prev_inflow if prev_inflow > 0 else 1
            
            if rank_improvement >= 5 and inflow_ratio >= 1.5:
                signal = {
                    'type': 'ignition',
                    'stock_name': stock_name,
                    'rank': current_rank,
                    'rank_improvement': rank_improvement,
                    'inflow_ratio': inflow_ratio,
                    'timestamp': current_time
                }
                signals.append(signal)
        
        return signals

    def _detect_sustained_attack_signals(self, current_data, current_time):
        """检测持续攻击信号"""
        sustained_signals = []
        
        for stock_name, history in self.long_term_stock_data.items():
            if len(history) < SUSTAINED_MIN_DATA_POINTS:
                continue
                
            # 检查是否在合理排名范围内
            latest_rank = history[-1]['rank']
            if latest_rank > SUSTAINED_RANK_THRESHOLD:
                continue
            
            # 检查资金流入稳定性
            positive_points = sum(1 for point in history if point['inflow'] > 0)
            stability_ratio = positive_points / len(history)
            
            if stability_ratio >= SUSTAINED_INFLOW_STABILITY_RATIO:
                signal = {
                    'type': 'sustained_attack',
                    'stock_name': stock_name,
                    'current_rank': latest_rank,
                    'stability_ratio': stability_ratio,
                    'window_points': len(history),
                    'timestamp': current_time
                }
                sustained_signals.append(signal)
        
        return sustained_signals

def analyze_market_state(inflows):
    """分析市场状态：资金规模、集中度、分散度"""
    inflows_yi = [x / 1e8 for x in inflows[:5]]
    total_top5 = sum(inflows_yi)

    if total_top5 > 80:
        scale = "超大资金"
    elif total_top5 > 40:
        scale = "大资金"
    else:
        scale = "小资金"

    concentration = sum(inflows_yi[:2]) / total_top5 if total_top5 > 0 else 0
    dispersion = np.std(inflows_yi) / np.mean(inflows_yi) if np.mean(inflows_yi) > 0 else 0

    return {"scale": scale, "concentration": concentration, "dispersion": dispersion,
            "total_top5": total_top5, "avg_top5": np.mean(inflows_yi)}


def calculate_dynamic_thresholds(market_state):
    """根据市场状态动态计算判断阈值"""
    scale_factors = {"超大资金": 1.15, "大资金": 1.25, "小资金": 1.45}
    concentration_adj = (1 - market_state["concentration"]) * 0.1
    min_rel_gap = scale_factors[market_state["scale"]] + concentration_adj
    min_abs_gap_ratio = 0.15 + market_state["dispersion"] * 0.1
    min_abs_gap = market_state["avg_top5"] * min_abs_gap_ratio

    return {"min_relative_gap": min_rel_gap, "min_absolute_gap": min_abs_gap,
            "group_cohesion_limit": 1.4, "min_gap_score": 1.5}


def find_all_gap_points(inflows):
    """全局扫描，计算所有潜在断层点的综合得分"""
    gap_scores = []
    for i in range(min(6, len(inflows) - 1)):
        abs_gap = inflows[i] - inflows[i + 1]
        rel_gap = inflows[i] / inflows[i + 1] if inflows[i + 1] > 0 else float('inf')
        position_weight = 1.0 / (i / 2 + 1)
        gap_score = (abs_gap / 1e8) * rel_gap * position_weight
        gap_scores.append({"position": i, "abs_gap": abs_gap, "rel_gap": rel_gap, "score": gap_score})

    return max(gap_scores, key=lambda x: x["score"]) if gap_scores else None


def identify_leading_group(inflows, gap_position, names):
    """根据断层点识别领先集团及其特征"""
    leading_group = inflows[:gap_position + 1]
    leading_names = names[:gap_position + 1]
    max_internal_gap = 1.0
    if len(leading_group) > 1:
        internal_gaps = [leading_group[j] / leading_group[j + 1] for j in range(len(leading_group) - 1)]
        max_internal_gap = max(internal_gaps) if internal_gaps else 1.0

    return {"size": len(leading_group), "members": leading_names, "internal_cohesion": max_internal_gap}


def comprehensive_evaluation(market_state, max_gap, group_info, thresholds):
    """多维度综合评判，计算最终得分"""
    scores = {}
    scores["relative"] = min(max_gap["rel_gap"] / thresholds["min_relative_gap"], 2.0)
    scores["absolute"] = min((max_gap["abs_gap"] / 1e8) / thresholds["min_absolute_gap"], 2.0)
    scores["cohesion"] = 2.0 if group_info["internal_cohesion"] <= thresholds["group_cohesion_limit"] else 0.0
    scores["pattern"] = 1.8 if market_state["concentration"] > 0.65 else 1.2
    scores["position"] = 2.0 * (1.0 / (max_gap["position"] / 2 + 1))

    weights = {"relative": 0.22, "absolute": 0.22, "cohesion": 0.18, "pattern": 0.18, "position": 0.20}
    return sum(scores[key] * weights[key] for key in weights)


def analyze_stock_flow_gap(df_stocks, current_time=None, data_dir=None, ignition_detector=None, file_format=None, market_snapshot=None):
    """个股资金流断层分析主函数 - 与dynamic_gap_detector.py兼容版本"""
    
    try:
        # 1. 数据预处理 - 使用格式感知的过滤策略
        if file_format:
            positive_flow_df = apply_stock_filter(df_stocks, file_format)
        else:
            # 兼容旧调用，使用默认过滤策略
            positive_flow_df = df_stocks[df_stocks['今日主力净流入-净额'] > 0]
            
        if len(positive_flow_df) < 5:
            return "【个股资金流分析】: 数据不足，无法分析。"

        # 2. 点火信号检测（使用传入的、持久化的检测器实例）
        if ignition_detector is None:
            # 如果外部没有提供检测器，创建一个临时的
            ignition_detector = StockFlowIgnitionDetector()

        # 确保时间类型正确
        if current_time is not None:
            debug_print(f"[DEBUG] current_time类型: {type(current_time)}, 值: {current_time}")

        try:
            ignition_signals = ignition_detector.detect_ignition_signals(positive_flow_df, current_time, market_snapshot)
        except Exception as e:
            print(f"[ERROR] 点火信号检测失败: {e}")
            print(f"[ERROR] 详细错误: {str(e)}")
            ignition_signals = []
    except Exception as e:
        print(f"[ERROR] 数据预处理失败: {e}")
        return f"【个股资金流分析】: 分析失败 - {e}"

    # 安全地获取净流入数据
    if '今日主力净流入-净额' in positive_flow_df.columns:
        net_inflow_col = '今日主力净流入-净额'
    else:
        # 查找可能的替代列名
        possible_columns = [col for col in positive_flow_df.columns if '净流入' in col or 'net_inflow' in col.lower()]
        if possible_columns:
            net_inflow_col = possible_columns[0]
            print(f"[INFO] 使用列 '{net_inflow_col}' 作为净流入数据")
        else:
            print(f"[ERROR] 未找到净流入相关列: {list(positive_flow_df.columns)}")
            return "【个股资金流分析】: 缺少净流入数据列，无法分析。"

    inflows = positive_flow_df.head(50)[net_inflow_col].tolist()
    names = positive_flow_df.head(50)['名称'].tolist()

    market_state = analyze_market_state(inflows)
    thresholds = calculate_dynamic_thresholds(market_state)
    max_gap = find_all_gap_points(inflows)

    if not max_gap:
        return "【个股资金流分析】: 未找到潜在断层点。"

    group_info = identify_leading_group(inflows, max_gap["position"], names)
    total_score = comprehensive_evaluation(market_state, max_gap, group_info, thresholds)

    if total_score >= thresholds["min_gap_score"]:
        # 发现断层的详细报告
        report = [f"【★★★★★ 个股资金流发现资金断层! ★★★★★】"]
        leader_desc = f"断层龙头: 【{group_info['members'][0]}】" if group_info[
                                                                       "size"] == 1 else f"领先集团: 【{', '.join(group_info['members'])}】"
        report.append(f"  {leader_desc}")
        report.append(f"  断层位置: 在第 {max_gap['position'] + 1} 名后")
        report.append(f"  绝对差距: {format_amount(max_gap['abs_gap'])}")
        report.append(f"  相对差距: {max_gap['rel_gap']:.2f} 倍")
        report.append(f"  市场状态: {market_state['scale']}市场 (集中度 {market_state['concentration']:.1%})")
        report.append(f"  综合得分: {total_score:.2f} (阈值 {thresholds['min_gap_score']:.1f})")
        return "\n".join(report)
    else:
        # 未发现断层的报告（与dynamic_gap_detector.py保持一致）
        leader_name = group_info["members"][0] if group_info["members"] else "未知"
        report = [f"【--- 个股资金流未发现显著资金断层 ---】"]
        report.append(f"  当前龙头: 【{leader_name}】")
        report.append(f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}")
        report.append(f"  综合得分: {total_score:.2f}分 (未达到{thresholds['min_gap_score']:.1f}分阈值)")
        return "\n".join(report)


# ==============================================================================
# § C. 板块内部龙头结构分析模块 (移植自 dynamic_gap_detector.py)
# ==============================================================================

def find_concept_summary_file(concept_name, current_time, data_dir, file_manifest=None):
    """
    查找指定概念在指定时间的概念文件（优化版，支持文件索引）
    """
    # 优先使用文件索引
    if file_manifest:
        # 清理概念名称，去掉可能的"概念"后缀
        clean_concept_name = concept_name.replace('概念', '') if concept_name.endswith('概念') else concept_name

        # 尝试精确匹配
        result = find_latest_file_from_manifest(file_manifest, 'concept_summary', current_time, concept_name)
        if result:
            return result

        # 尝试清理后的名称匹配
        result = find_latest_file_from_manifest(file_manifest, 'concept_summary', current_time, clean_concept_name)
        if result:
            return result

        # 尝试模糊匹配
        for ts_str, files_dict in file_manifest.items():
            try:
                file_time = datetime.strptime(ts_str, '%H%M%S').time()
                if file_time <= current_time and 'concept_summary' in files_dict:
                    concept_files = files_dict['concept_summary']
                    if isinstance(concept_files, dict):
                        for file_concept_name, filename in concept_files.items():
                            # 双向包含匹配
                            if ((clean_concept_name in file_concept_name and len(clean_concept_name) >= 3) or
                                (file_concept_name in clean_concept_name and len(file_concept_name) >= 3)):
                                return filename
            except ValueError:
                continue

        return None

    # 回退到原始文件扫描方式（保持向后兼容）
    try:
        all_files = os.listdir(data_dir)

        # 清理概念名称，去掉可能的"概念"后缀
        clean_concept_name = concept_name.replace('概念', '') if concept_name.endswith('概念') else concept_name

        # 查找匹配的概念文件，支持多种格式
        concept_files = []
        for f in all_files:
            if f.endswith('.csv'):
                # 精确匹配
                if (f.startswith(f'concept_summary_{concept_name}_') or
                    f.startswith(f'concept_summary_{clean_concept_name}_') or
                    f.startswith(f'概念_stocks_{concept_name}_') or
                    f.startswith(f'概念_stocks_{clean_concept_name}_')):
                    concept_files.append(f)
                # 模糊匹配：只进行合理的包含匹配，避免错误匹配
                elif (f.startswith('concept_summary_') or f.startswith('概念_stocks_')):
                    file_parts = f.split('_')
                    if len(file_parts) >= 3:
                        file_concept_name = file_parts[2]
                        # 只进行双向包含匹配，且要求匹配长度足够长以避免误匹配
                        if ((clean_concept_name in file_concept_name and len(clean_concept_name) >= 3) or
                            (file_concept_name in clean_concept_name and len(file_concept_name) >= 3)):
                            concept_files.append(f)

        if not concept_files:
            return None

        # 使用find_latest_file逻辑找到最新的文件
        return find_latest_file(concept_files, current_time)

    except Exception as e:
        print(f"查找概念文件失败: {e}")
        return None


def find_sector_summary_file(sector_name, current_time, data_dir, file_manifest=None):
    """
    查找指定板块在指定时间的行业文件（优化版，支持文件索引）
    """
    # 优先使用文件索引
    if file_manifest:
        # 尝试精确匹配
        result = find_latest_file_from_manifest(file_manifest, 'sector_summary', current_time, sector_name)
        if result:
            return result

        # 尝试模糊匹配
        for ts_str, files_dict in file_manifest.items():
            try:
                file_time = datetime.strptime(ts_str, '%H%M%S').time()
                if file_time <= current_time and 'sector_summary' in files_dict:
                    sector_files = files_dict['sector_summary']
                    if isinstance(sector_files, dict):
                        for file_sector_name, filename in sector_files.items():
                            # 双向包含匹配
                            if ((sector_name in file_sector_name and len(sector_name) >= 3) or
                                (file_sector_name in sector_name and len(file_sector_name) >= 3)):
                                return filename
            except ValueError:
                continue

        return None

    # 回退到原始文件扫描方式（保持向后兼容）
    try:
        # 不使用文件缓存，确保每次都获取最新的文件列表，避免未来函数风险
        all_files = os.listdir(data_dir)

        # 查找匹配的行业文件，支持多种格式
        sector_files = []
        for f in all_files:
            if f.endswith('.csv'):
                # 精确匹配
                if (f.startswith(f'sector_summary_{sector_name}_') or
                    f.startswith(f'行业_stocks_{sector_name}_')):
                    sector_files.append(f)
                # 模糊匹配：只进行合理的包含匹配，避免错误匹配
                elif (f.startswith('sector_summary_') or f.startswith('行业_stocks_')):
                    file_parts = f.split('_')
                    if len(file_parts) >= 3:
                        file_sector_name = file_parts[2]
                        # 只进行双向包含匹配，且要求匹配长度足够长以避免误匹配
                        if ((sector_name in file_sector_name and len(sector_name) >= 3) or
                            (file_sector_name in sector_name and len(file_sector_name) >= 3)):
                            sector_files.append(f)

        if not sector_files:
            return None

        # 使用find_latest_file逻辑找到最新的文件
        return find_latest_file(sector_files, current_time)

    except Exception as e:
        print(f"查找行业文件失败: {e}")
        return None


def parse_internal_data(file_path):
    """解析板块/概念内部文件，返回排序后的正流入个股数据"""
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig')

        # 查找净流入列
        net_inflow_col = None
        if '今日主力净流入-净额' in df.columns:
            net_inflow_col = '今日主力净流入-净额'
        else:
            # 查找可能的替代列名
            possible_columns = [col for col in df.columns if '净流入' in col or 'net_inflow' in col.lower()]
            if possible_columns:
                net_inflow_col = possible_columns[0]

        if net_inflow_col is None:
            print(f"[WARNING] 文件 {file_path} 中未找到净流入列")
            return None

        df[net_inflow_col] = df[net_inflow_col].apply(convert_to_float)
        positive_df = df[df[net_inflow_col] > 0].copy()

        # 如果使用的不是标准列名，重命名为标准列名以保持兼容性
        if net_inflow_col != '今日主力净流入-净额':
            positive_df = positive_df.rename(columns={net_inflow_col: '今日主力净流入-净额'})

        return positive_df.sort_values(by='今日主力净流入-净额', ascending=False)
    except Exception as e:
        print(f"[ERROR] 解析内部数据文件失败: {e}")
        return None


def generate_internal_analysis_report(df_internal, sector_name, sector_file, sector_total_amount, current_time):
    """生成板块内部个股资金流分析报告（支持双龙头检测和占比判断）- 完整版"""
    try:
        # 取前10名用于分析
        top_stocks = df_internal.head(10)

        if len(top_stocks) < 2:
            return f"  \n--- {sector_name}内部个股分析 ---\n  数据不足，仅有{len(top_stocks)}只正流入个股"

        # 计算内部断层 - 增强版（支持双龙头检测+占比判断）
        inflows = top_stocks['今日主力净流入-净额'].tolist()
        names = top_stocks['名称'].tolist()

        # 尝试获取股票代码（如果数据中包含）
        codes = []
        if '代码' in top_stocks.columns:
            codes = top_stocks['代码'].tolist()
        else:
            # 简化版代码映射
            stock_name_to_code = {
                '科大讯飞': '002230', '寒武纪-U': '688256', '兆易创新': '603986',
                '阿石创': '300706', '宁波银行': '002142', '招商银行': '600036'
            }
            codes = [stock_name_to_code.get(name, '000000') for name in names]
        
        # 基础比例计算
        first_ratio = inflows[0] / inflows[1] if inflows[1] > 0 else 1.0
        first_amount = inflows[0] / 1e8  # 第1名资金量（亿）
        second_amount = inflows[1] / 1e8  # 第2名资金量（亿）
        first_second_gap = (inflows[0] - inflows[1]) / 1e8  # 第1-2名差距（亿）
        
        # 【新增】占比判断逻辑
        first_ratio_sector = (first_amount / sector_total_amount) * 100 if sector_total_amount > 0 else 0
        second_ratio_sector = (second_amount / sector_total_amount) * 100 if sector_total_amount > 0 else 0
        dual_ratio_sector = first_ratio_sector + second_ratio_sector
        
        # 如果有第3名，计算第2-3名关系
        has_third = len(inflows) >= 3 and inflows[2] > 0
        if has_third:
            second_third_ratio = inflows[1] / inflows[2]
            third_amount = inflows[2] / 1e8
            second_third_gap = (inflows[1] - inflows[2]) / 1e8
            third_ratio_sector = (third_amount / sector_total_amount) * 100 if sector_total_amount > 0 else 0
        else:
            second_third_ratio = float('inf')
            third_amount = 0
            second_third_gap = 0
            third_ratio_sector = 0
        
        # 结构类型判断 - 增强版判断逻辑
        structure_type, core_stocks, structure_detail = _determine_internal_structure(
            names, inflows, first_ratio, second_third_ratio if has_third else float('inf'),
            first_ratio_sector, second_ratio_sector, dual_ratio_sector, sector_total_amount
        )

        # 生成详细报告
        report_lines = [f"  \n--- {sector_name}内部个股分析 ---"]
        report_lines.append(f"  内部格局: 【{structure_type}】")
        
        if core_stocks:
            report_lines.append(f"  核心{sector_name}: {', '.join(core_stocks)}")
        
        # 添加关键指标
        report_lines.append(f"  前2股占{sector_name}比: {dual_ratio_sector:.1f}%")
        report_lines.append(f"  第1名vs第2名: {first_ratio:.2f}倍")
        
        if has_third:
            report_lines.append(f"  第2名vs第3名: {second_third_ratio:.2f}倍")
        
        # 【新增：核心概念/行业详细信息】
        if core_stocks:
            report_lines.append(f"  ★ 核心{sector_name}股票详情:")
            for i, stock_name in enumerate(core_stocks):
                if i < len(names) and i < len(inflows):
                    amount_yi = inflows[i] / 1e8
                    sector_pct = (amount_yi / sector_total_amount) * 100 if sector_total_amount > 0 else 0
                    code = codes[i] if i < len(codes) else '000000'
                    report_lines.append(f"    → {stock_name}({code}): {amount_yi:.2f}亿 ({sector_pct:.1f}%占{sector_name})")
        else:
            report_lines.append(f"  ★ {sector_name}资金分散，无明显核心股票")
        
        # 个股详情（前5名）
        report_lines.append("  个股详情:")
        for i, (name, inflow) in enumerate(zip(names[:5], inflows[:5])):
            amount_yi = inflow / 1e8
            sector_pct = (amount_yi / sector_total_amount) * 100 if sector_total_amount > 0 else 0
            code = codes[i] if i < len(codes) else '000000'
            report_lines.append(f"    {i+1}. {name}({code}): {amount_yi:.2f}亿 ({sector_pct:.1f}%)")

        return "\n".join(report_lines)
        
    except Exception as e:
        return f"  \n--- {sector_name}内部个股分析 ---\n  生成报告失败: {e}"

def _determine_internal_structure(names, inflows, first_ratio, second_third_ratio, 
                                first_ratio_sector, second_ratio_sector, dual_ratio_sector, sector_total_amount):
    """确定内部结构类型 - 参考dynamic_gap_detector.py的逻辑"""
    
    # 【优先级1】绝对龙头判断 - 基于板块占比的绝对优势
    if first_ratio_sector >= 50.0:
        return "绝对龙头", [names[0]], f"第1名占板块{first_ratio_sector:.1f}%"
    
    # 【优先级2】强势单股判断 - 基于比例+占比的双重条件
    if first_ratio_sector >= 40.0 and first_ratio >= 1.3:
        return "绝对龙头", [names[0]], f"强势单股（占比{first_ratio_sector:.1f}%, {first_ratio:.2f}倍优势）"
    
    # 【优先级3】三足鼎立检测 - 需要在双龙头检测之前
    if len(names) >= 3:
        third_ratio_sector = (inflows[2] / 1e8 / sector_total_amount) * 100 if sector_total_amount > 0 else 0
        triple_ratio_sector = first_ratio_sector + second_ratio_sector + third_ratio_sector
        
        # 三足鼎立条件（参考dynamic_gap_detector.py）
        triple_close = (first_ratio < 1.35 and second_third_ratio < 1.15)  # 内部接近
        triple_dominance = triple_ratio_sector >= 50.0  # 前三合占比足够
        third_significant = third_ratio_sector >= 12.0  # 第3名占比不能太小
        
        if triple_close and triple_dominance and third_significant:
            return "三足鼎立", names[:3], f"三强竞争（前3占{triple_ratio_sector:.1f}%）"
    
    # 【优先级4】双龙头检测 - 参考dynamic_gap_detector.py的完整逻辑
    if len(names) >= 2:
        # 双龙头检测条件（基于dynamic_gap_detector.py）
        # 条件1：前两名合计占比足够高
        dual_dominance = dual_ratio_sector >= 35.0
        
        # 条件2：个股占比平衡（避免一强一弱）  
        balanced_shares = (first_ratio_sector >= 15.0 and 
                         second_ratio_sector >= 12.0 and
                         first_ratio_sector < 35.0)
        
        # 条件3：前两名相对接近
        close_leaders = first_ratio < 1.40
        
        # 条件4：与第3名有明显差距（如果存在第3名）
        clear_separation = True
        if len(names) >= 3 and second_third_ratio != float('inf'):
            clear_separation = second_third_ratio >= 1.15
        
        # 综合判断双龙头
        if dual_dominance and balanced_shares and close_leaders and clear_separation:
            return "双龙头", names[:2], f"双强格局（合占{dual_ratio_sector:.1f}%）"
        
        # 高占比双龙头（放宽条件）
        elif (dual_ratio_sector >= 45.0 and first_ratio < 1.50 and 
              second_ratio_sector >= 15.0 and second_third_ratio >= 1.05):
            return "双龙头", names[:2], f"高占比双龙头（合占{dual_ratio_sector:.1f}%）"
    
    # 【优先级5】单股龙头判断
    if first_ratio_sector >= 25.0 and first_ratio >= 1.25:
        return "单股龙头", [names[0]], f"单股领先（占比{first_ratio_sector:.1f}%）"
    elif first_ratio >= 1.15 and (inflows[0] - inflows[1]) / 1e8 >= 0.1:
        return "单股龙头", [names[0]], f"单股领先（{first_ratio:.2f}倍优势）"
    
    # 【默认】竞争激烈
    return "竞争激烈", [], f"资金分散（第1名仅占{first_ratio_sector:.1f}%）"


# ==============================================================================
# § A2. 分析信号检测函数（已移除买入功能，仅保留分析功能）
# ==============================================================================

# 注：原买入信号检测和执行相关函数已完全移除


def analyze_sector_internal_flow_wrapper(leader_name, sector_type, sector_total_amount, current_time, data_dir, file_manifest=None, data_cache=None):
    """板块内部龙头结构分析的包装函数（仅分析功能，已移除买入信号检测）"""
    # 检查是否显示内部分析
    if not SHOW_INTERNAL_ANALYSIS:
        return ""  # 如果关闭显示，返回空字符串

    internal_report = f"\n--- {leader_name} ({sector_type}) 内部个股分析 ---"

    # 根据板块类型选择相应的文件查找函数
    if sector_type == '概念':
        file_path = find_concept_summary_file(leader_name, current_time, data_dir, file_manifest)
        if not file_path:
            internal_report += f"\n  未找到{leader_name}概念文件"
            return internal_report
    else:  # 行业
        file_path = find_sector_summary_file(leader_name, current_time, data_dir, file_manifest)
        if not file_path:
            internal_report += f"\n  未找到对应的内部资金流数据文件"
            return internal_report

    # 使用缓存读取内部数据
    full_file_path = os.path.join(data_dir, file_path)
    if data_cache is not None:
        df_internal = parse_internal_data_cached(full_file_path, data_cache)
    else:
        df_internal = parse_internal_data(full_file_path)

    if df_internal is None or df_internal.empty:
        internal_report += "\n  无正流入个股数据"
        return internal_report

    internal_report = generate_internal_analysis_report(df_internal, leader_name, file_path, sector_total_amount, current_time)

    # 打印Top 5个股列表
    top5_internal = df_internal.head(5)
    table_data = []
    for i, (_, row) in enumerate(top5_internal.iterrows(), 1):
        table_data.append([i, row['名称'], format_amount(row['今日主力净流入-净额'])])

    internal_report += "\n" + tabulate(table_data, headers=['排名', '股票名称', '净流入'], tablefmt='psql',
                                       showindex=False)

    return internal_report


# ==============================================================================
# § D. 涨停股池分析模块 (移植自 dynamic_gap_detector.py)
# ==============================================================================

def parse_limit_up_pool_data(file_path):
    """解析涨停股池文件，返回涨停股票信息"""
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip', low_memory=False)
        # 查找可能的列名
        name_columns = ['名称', 'name', '股票名称', '证券名称']
        code_columns = ['代码', 'code', '股票代码', '证券代码'] 
        price_columns = ['最新价', 'price', '现价', '股价']
        # 新增列名映射
        additional_columns = {
            '成交额': ['成交额', 'volume', '成交量'],
            '封板资金': ['封板资金', 'seal_amount', '封单金额'], 
            '首次封板时间': ['首次封板时间', 'first_seal_time', '首封时间'],
            '最后封板时间': ['最后封板时间', 'last_seal_time', '末封时间'],
            '炸板次数': ['炸板次数', 'break_count', '开板次数'],
            '涨停统计': ['涨停统计', 'limit_up_stat', '涨停次数'],
            '连板数': ['连板数', 'consecutive_days', '连续涨停'],
            '所属行业': ['所属行业', 'industry', '行业', '板块']
        }
        
        name_col = next((col for col in name_columns if col in df.columns), None)
        code_col = next((col for col in code_columns if col in df.columns), None) 
        price_col = next((col for col in price_columns if col in df.columns), None)
        
        if name_col:
            result = []
            for _, row in df.iterrows():
                stock_info = {'名称': row[name_col]}
                if code_col and pd.notna(row[code_col]):
                    stock_info['代码'] = row[code_col]
                if price_col and pd.notna(row[price_col]):
                    stock_info['最新价'] = row[price_col]
                # 添加新的字段
                for target_col, possible_names in additional_columns.items():
                    for possible_name in possible_names:
                        if possible_name in df.columns and pd.notna(row[possible_name]):
                            stock_info[target_col] = row[possible_name]
                            break
                result.append(stock_info)
            return result
        return []
    except Exception as e:
        print(f"解析涨停股池文件失败: {e}")
        return []


def extract_limit_up_key_sectors(current_limit_up_stocks, stock_sector_map=None):
    """提取涨停池中的关键行业和概念，返回需要进行内部分析的行业和概念列表（优化版）"""
    if not current_limit_up_stocks:
        return [], []

    # 统计涨停行业分布
    industry_count = {}
    concept_count = {}

    for stock in current_limit_up_stocks:
        industry = stock.get('所属行业', '未知')
        if industry != '未知':
            industry_count[industry] = industry_count.get(industry, 0) + 1

        # 获取概念信息（使用预加载的映射表）
        stock_name = stock.get('名称', '')
        sectors_info = get_stock_sectors(stock_name, stock_sector_map=stock_sector_map)
        if sectors_info and sectors_info.get('concepts'):
            for concept in sectors_info.get('concepts', [])[:3]:  # 只取前3个概念
                concept_count[concept] = concept_count.get(concept, 0) + 1

    # 统计连板数分布
    consecutive_stats = {}
    consecutive_concept_stats = {}
    
    for stock in current_limit_up_stocks:
        consecutive_days = stock.get('连板数', 1)
        try:
            consecutive_days = int(consecutive_days) if pd.notna(consecutive_days) else 1
        except:
            consecutive_days = 1
            
        industry = stock.get('所属行业', '未知')
        
        if consecutive_days not in consecutive_stats:
            consecutive_stats[consecutive_days] = {}
            consecutive_concept_stats[consecutive_days] = {}
        
        if industry != '未知':
            consecutive_stats[consecutive_days][industry] = consecutive_stats[consecutive_days].get(industry, 0) + 1
        
        # 概念统计
        stock_name = stock.get('名称', '')
        sectors_info = get_stock_sectors(stock_name, stock_sector_map=stock_sector_map)
        if sectors_info and sectors_info.get('concepts'):
            for concept in sectors_info.get('concepts', [])[:3]:
                consecutive_concept_stats[consecutive_days][concept] = consecutive_concept_stats[consecutive_days].get(concept, 0) + 1

    # 收集关键行业和概念
    key_industries = set()
    key_concepts = set()
    
    # 1. 涨停行业最多的
    if industry_count:
        max_industry = max(industry_count.items(), key=lambda x: x[1])
        if max_industry[1] >= 2:  # 至少2个涨停股
            key_industries.add(max_industry[0])
    
    # 2. 从连板统计中提取关键行业
    if consecutive_stats:
        max_consecutive = max(consecutive_stats.keys())
        
        # 连板数最多行业
        if max_consecutive > 1 and max_consecutive in consecutive_stats:
            max_consecutive_industry = max(consecutive_stats[max_consecutive].items(), key=lambda x: x[1])
            if max_consecutive_industry[1] >= 2:
                key_industries.add(max_consecutive_industry[0])
        
        # 首板最多行业
        if 1 in consecutive_stats:
            max_first_board_industry = max(consecutive_stats[1].items(), key=lambda x: x[1])
            if max_first_board_industry[1] >= 2:
                key_industries.add(max_first_board_industry[0])
        
        # 2板、3板等最多的行业
        for consecutive_days in sorted(consecutive_stats.keys()):
            if consecutive_days > 1 and consecutive_days < max_consecutive:
                max_industry = max(consecutive_stats[consecutive_days].items(), key=lambda x: x[1])
                if max_industry[1] >= 2:
                    key_industries.add(max_industry[0])

    # 3. 从概念统计中提取关键概念
    if concept_count:
        # 获取概念数量最多的前3个
        top_concepts = sorted(concept_count.items(), key=lambda x: x[1], reverse=True)[:3]
        for concept, count in top_concepts:
            if count >= 2:  # 至少2个涨停股
                key_concepts.add(concept)
    
    # 4. 从连板概念统计中提取关键概念
    for consecutive_days, concept_stats in consecutive_concept_stats.items():
        if concept_stats:
            top_concepts = sorted(concept_stats.items(), key=lambda x: x[1], reverse=True)[:2]
            for concept, count in top_concepts:
                if count >= 2:
                    key_concepts.add(concept)
    
    return list(key_industries), list(key_concepts)


def _display_stock_sectors_info(stock_names, stock_list, stock_sector_map, action_type):
    """显示股票的行业和概念信息"""
    if not stock_names or not stock_sector_map:
        return

    for stock_name in stock_names:
        # 从股票列表中找到对应的股票信息
        stock_info = None
        for stock in stock_list:
            if stock.get('名称', '') == stock_name:
                stock_info = stock
                break

        # 获取行业信息
        industry = stock_info.get('所属行业', '未知') if stock_info else '未知'

        # 获取概念信息
        sectors_info = get_stock_sectors(stock_name, stock_sector_map=stock_sector_map)
        concepts = []
        if sectors_info and sectors_info.get('concepts'):
            # 过滤有意义的概念，只取前3个
            meaningful_concepts = [c for c in sectors_info.get('concepts', []) if is_meaningful_concept(c)]
            concepts = meaningful_concepts[:3]

        # 格式化显示
        concept_str = ', '.join(concepts) if concepts else '无'
        print(f"    {stock_name}: 行业[{industry}] 概念[{concept_str}]")


def _check_monitored_industries_alert(new_limit_up_stocks, current_limit_up_stocks, hot_industries_stats):
    """检查重点监控行业的新增涨停提醒"""
    if not new_limit_up_stocks or not hot_industries_stats:
        return

    # 获取当前的首板最多行业和2板连板数最多行业
    monitored_industries = []

    # 从热点行业统计中提取监控行业
    if '首扳最多行业' in hot_industries_stats:
        monitored_industries.append(hot_industries_stats['首扳最多行业'])
    if '2板连板数最多行业' in hot_industries_stats:
        monitored_industries.append(hot_industries_stats['2板连板数最多行业'])

    # 检查新增涨停股是否属于监控行业
    for stock_name in new_limit_up_stocks:
        # 找到对应的股票信息
        stock_info = None
        for stock in current_limit_up_stocks:
            if stock.get('名称', '') == stock_name:
                stock_info = stock
                break

        if stock_info:
            industry = stock_info.get('所属行业', '未知')
            for monitored_industry in monitored_industries:
                if industry == monitored_industry:
                    print(f"🚨 重点监控提醒: {stock_name} 属于监控行业 [{industry}] 新增涨停!")


def display_limit_up_changes_and_stats(current_limit_up_stocks, previous_limit_up_stocks, stock_sector_map=None, hot_industries_stats=None):
    """显示涨停变化和统计信息"""
    # 显示新增和退出涨停股
    if previous_limit_up_stocks:
        current_names = {stock.get('名称', '') for stock in current_limit_up_stocks}
        previous_names = {stock.get('名称', '') for stock in previous_limit_up_stocks}

        new_limit_up = [name for name in current_names if name not in previous_names]
        removed_limit_up = [name for name in previous_names if name not in current_names]

        if new_limit_up:
            print(f"[*] 新增涨停: {', '.join(new_limit_up)}")
            # 显示新增涨停股的行业和概念信息
            _display_stock_sectors_info(new_limit_up, current_limit_up_stocks, stock_sector_map, "新增涨停")
            # 检查是否有重点监控行业的新增涨停
            _check_monitored_industries_alert(new_limit_up, current_limit_up_stocks, hot_industries_stats)

        if removed_limit_up:
            print(f"[COLD] 退出涨停: {', '.join(removed_limit_up)}")
            # 显示退出涨停股的行业和概念信息
            _display_stock_sectors_info(removed_limit_up, previous_limit_up_stocks, stock_sector_map, "退出涨停")
    
    # 统计涨停行业分布
    industry_count = {}
    concept_count = {}
    
    for stock in current_limit_up_stocks:
        industry = stock.get('所属行业', '未知')
        industry_count[industry] = industry_count.get(industry, 0) + 1
        
        # 获取概念信息
        stock_name = stock.get('名称', '')
        sectors_info = get_stock_sectors(stock_name, stock_sector_map=stock_sector_map)
        if sectors_info and sectors_info.get('concepts'):
            for concept in sectors_info.get('concepts', [])[:3]:  # 只取前3个概念
                concept_count[concept] = concept_count.get(concept, 0) + 1

    # 涨停行业最多的
    if industry_count:
        max_industry = max(industry_count.items(), key=lambda x: x[1])
        # 找出概念中数量最多的
        concept_display = ""
        if concept_count:
            top_concepts = sorted(concept_count.items(), key=lambda x: x[1], reverse=True)[:3]
            if top_concepts:
                max_count = top_concepts[0][1]
                max_concepts = [f"{concept}{count}个" for concept, count in top_concepts if count == max_count and count >= 2]
                concept_display = f"    |   {' | '.join(max_concepts)}|" if max_concepts else ""
        print(f"涨停行业最多的：{max_industry[0]}  {max_industry[1]}个{concept_display}")

    # 统计连板数分布
    consecutive_stats = {}
    consecutive_concept_stats = {}
    
    for stock in current_limit_up_stocks:
        consecutive_days = stock.get('连板数', 1)
        try:
            consecutive_days = int(consecutive_days) if pd.notna(consecutive_days) else 1
        except:
            consecutive_days = 1
            
        industry = stock.get('所属行业', '未知')
        
        if consecutive_days not in consecutive_stats:
            consecutive_stats[consecutive_days] = {}
            consecutive_concept_stats[consecutive_days] = {}
        
        consecutive_stats[consecutive_days][industry] = consecutive_stats[consecutive_days].get(industry, 0) + 1
        
        # 概念统计
        stock_name = stock.get('名称', '')
        sectors_info = get_stock_sectors(stock_name, stock_sector_map=stock_sector_map)
        if sectors_info and sectors_info.get('concepts'):
            for concept in sectors_info.get('concepts', [])[:3]:
                consecutive_concept_stats[consecutive_days][concept] = consecutive_concept_stats[consecutive_days].get(concept, 0) + 1

    if consecutive_stats:
        # 连板数最多行业
        max_consecutive = max(consecutive_stats.keys())
        if max_consecutive > 1:
            max_consecutive_industry = max(consecutive_stats[max_consecutive].items(), key=lambda x: x[1])
            concept_display = ""
            if max_consecutive in consecutive_concept_stats:
                top_concepts = sorted(consecutive_concept_stats[max_consecutive].items(), key=lambda x: x[1], reverse=True)[:3]
                if top_concepts:
                    max_count = top_concepts[0][1]
                    max_concepts = [f"{concept}{count}个" for concept, count in top_concepts if count == max_count and count >= 2]
                    concept_display = f"    |   {' | '.join(max_concepts)}|" if max_concepts else ""
            print(f"连板数最多行业：{max_consecutive_industry[0]}  {max_consecutive_industry[1]}个  {max_consecutive}连扳{concept_display}")

        # 首扳最多行业
        if 1 in consecutive_stats:
            max_first_board_industry = max(consecutive_stats[1].items(), key=lambda x: x[1])
            concept_display = ""
            if 1 in consecutive_concept_stats:
                top_concepts = sorted(consecutive_concept_stats[1].items(), key=lambda x: x[1], reverse=True)[:3]
                if top_concepts:
                    max_count = top_concepts[0][1]
                    max_concepts = [f"{concept}{count}个" for concept, count in top_concepts if count == max_count and count >= 2]
                    concept_display = f"    |   {' | '.join(max_concepts)}|" if max_concepts else ""
            print(f"首扳最多行业：{max_first_board_industry[0]}  {max_first_board_industry[1]}个  首扳{concept_display}")

        # 2板、3板等最多的行业
        for consecutive_days in sorted(consecutive_stats.keys()):
            if consecutive_days > 1 and consecutive_days < max_consecutive:
                max_industry = max(consecutive_stats[consecutive_days].items(), key=lambda x: x[1])
                concept_display = ""
                if consecutive_days in consecutive_concept_stats:
                    top_concepts = sorted(consecutive_concept_stats[consecutive_days].items(), key=lambda x: x[1], reverse=True)[:3]
                    if top_concepts:
                        max_count = top_concepts[0][1]
                        max_concepts = [f"{concept}{count}个" for concept, count in top_concepts if count == max_count and count >= 2]
                        concept_display = f"    |   {' | '.join(max_concepts)}|" if max_concepts else ""
                print(f"{consecutive_days}板连板数最多行业：{max_industry[0]}  {max_industry[1]}个  {consecutive_days}连扳{concept_display}")


def _get_risk_warning_for_stock(stock, hot_industries_info, stock_sector_map=None):
    """获取股票的风险警示信息"""
    warnings = []

    # 获取股票行业
    industry = stock.get('所属行业', '未知')

    # 检查是否属于热点行业但资金流出
    if industry in hot_industries_info:
        industry_info = hot_industries_info[industry]
        if industry_info.get('net_inflow', 0) < 0:  # 资金流出
            warnings.append('资金流出')

    # 获取概念信息并检查
    if stock_sector_map:
        stock_name = stock.get('名称', '')
        sectors_info = get_stock_sectors(stock_name, stock_sector_map=stock_sector_map)
        if sectors_info and sectors_info.get('concepts'):
            for concept in sectors_info.get('concepts', [])[:3]:
                if concept in hot_industries_info:
                    concept_info = hot_industries_info[concept]
                    if concept_info.get('net_inflow', 0) < 0:  # 概念资金流出
                        warnings.append(f'{concept}流出')
                        break  # 只显示第一个流出的概念

    return ' | '.join(warnings) if warnings else ''


def display_limit_up_pool_table(current_limit_up_stocks, stock_flow_data=None, hot_industries_info=None, stock_sector_map=None):
    """显示涨停股池表格"""
    if not current_limit_up_stocks:
        print("暂无涨停股池数据。")
        return
        
    display_limit_up = current_limit_up_stocks[:20]  # 显示前20只
    
    # 如果涨停股池数据中没有资金流入信息，尝试从个股资金流数据中匹配
    if stock_flow_data is not None and not stock_flow_data.empty:
        for i, stock in enumerate(display_limit_up):
            stock_name = stock.get('名称', '')
            matching_flow = stock_flow_data[stock_flow_data['名称'] == stock_name]
            if not matching_flow.empty:
                flow_row = matching_flow.iloc[0]
                display_limit_up[i]['今日主力净流入-净额'] = flow_row.get('今日主力净流入-净额', 0)
                display_limit_up[i]['今日主力净流入-净占比'] = flow_row.get('今日主力净流入-净占比', 0)
                if '代码' not in display_limit_up[i] and '代码' in flow_row:
                    display_limit_up[i]['代码'] = flow_row['代码']

    limit_up_data = []
    for i, stock in enumerate(display_limit_up, 1):
        row = [i, stock['名称']]
        
        # 基础信息
        if '代码' in stock and pd.notna(stock['代码']):
            row.append(stock['代码'])
        else:
            row.append('N/A')
            
        if '最新价' in stock:
            row.append(f"{stock['最新价']:.2f}" if pd.notna(stock['最新价']) else 'N/A')
        else:
            row.append('N/A')
            
        # 成交额
        if '成交额' in stock:
            amount = stock['成交额']
            if pd.notna(amount) and amount > 0:
                row.append(f"{amount/1e8:.2f}亿" if amount >= 1e8 else f"{amount/1e4:.0f}万")
            else:
                row.append('N/A')
        else:
            row.append('N/A')
            
        # 封板资金
        if '封板资金' in stock:
            seal_amount = stock['封板资金']
            if pd.notna(seal_amount) and seal_amount > 0:
                row.append(f"{seal_amount/1e8:.2f}亿" if seal_amount >= 1e8 else f"{seal_amount/1e4:.0f}万")
            else:
                row.append('N/A')
        else:
            row.append('N/A')
            
        # 时间信息
        if '首次封板时间' in stock:
            row.append(str(stock['首次封板时间']) if pd.notna(stock['首次封板时间']) else 'N/A')
        else:
            row.append('N/A')
            
        if '最后封板时间' in stock:
            row.append(str(stock['最后封板时间']) if pd.notna(stock['最后封板时间']) else 'N/A')
        else:
            row.append('N/A')
            
        # 其他信息
        if '炸板次数' in stock:
            row.append(str(int(stock['炸板次数'])) if pd.notna(stock['炸板次数']) else '0')
        else:
            row.append('0')
            
        if '涨停统计' in stock:
            row.append(str(stock['涨停统计']) if pd.notna(stock['涨停统计']) else 'N/A')
        else:
            row.append('N/A')
            
        if '连板数' in stock:
            row.append(str(int(stock['连板数'])) if pd.notna(stock['连板数']) else '1')
        else:
            row.append('1')
            
        if '所属行业' in stock:
            row.append(str(stock['所属行业']) if pd.notna(stock['所属行业']) else 'N/A')
        else:
            row.append('N/A')
            
        # 添加资金流入信息（如果有）
        if '今日主力净流入-净额' in stock:
            inflow = stock['今日主力净流入-净额']
            if pd.notna(inflow) and inflow > 0:
                row.append('√')
            else:
                row.append('')
        else:
            row.append('')
            
        # 风险警示
        if hot_industries_info and stock_sector_map:
            risk_warning = _get_risk_warning_for_stock(stock, hot_industries_info, stock_sector_map)
            row.append(risk_warning)
        else:
            row.append('')

        limit_up_data.append(row)

    # 构建表头
    headers = ['排名', '股票名称', '股票代码', '最新价', '成交额', '封板资金', 
               '首次封板时间', '最后封板时间', '炸板次数', '涨停统计', '连板数', 
               '所属行业', '大于历史资金流入', '风险警示']
    
    # 设置最大列宽
    max_col_widths = [6, 10, 8, 8, 8, 8, 12, 12, 8, 8, 8, 10, 12, 10]
    print(tabulate(limit_up_data, headers=headers, tablefmt='psql', maxcolwidths=max_col_widths))


# ==============================================================================
# § D2. 预加载和缓存优化模块 (避免未来函数的I/O优化)
# ==============================================================================

def _build_preload_data(data_dir):
    """
    构建预加载数据：文件索引清单、数据缓存池、股票-板块映射表
    在回测开始前一次性执行，避免未来函数问题

    Returns:
        tuple: (file_manifest, data_cache, stock_sector_map)
    """
    print("🔄 开始构建预加载数据...")

    # ==================== 方案一：构建文件索引清单 ====================
    print("  📁 构建文件索引清单...")
    all_files = os.listdir(data_dir)
    print(f"    发现 {len(all_files)} 个文件")
    file_manifest = {}  # {timestamp: {file_type: filename}}

    # 定义文件类型匹配模式
    file_patterns = {
        'industry': [
            'industry_fund_flow_rank_',  # 行业资金流排名（必须在fund_flow_rank_之前）
            'sector_fund_flow_rank_', 'sector_fund_flow_tpdog.csv',
            'sector_fund_flow_akshare.csv', 'sector_fund_flow',
            '实时板块资金流_', 'realtime_sector_flow_'
        ],
        'concept': [
            'concept_fund_flow_rank_',    # 概念资金流排名
            'concept_fund_flow_tpdog.csv', 'concept_fund_flow_akshare.csv',
            'concept_fund_flow', '实时概念资金流_', 'realtime_concept_flow_'
        ],
        'stock_flow': [
            'fund_flow_rank_', 'fund_flow_tpdog.csv', 'ths_fund_flow.csv',
            'fund_flow_akshare.csv', 'individual_fund_flow_', '股票资金流_zssz_',
            '股票资金流_zssh_', '个股资金流_', 'stock_fund_flow_'
        ],
        'limit_up': [
            'limit_up_pool_', 'zt_pool.csv', '涨停股池_', 'limit_up_',
            # 匹配时间格式的涨停股池文件，如 09-33_zt_pool.csv
            r'\d{2}-\d{2}_zt_pool\.csv',
            # 匹配 zt_pool_ 但排除 zt_pool_previous_
            'zt_pool_'
        ],
        'concept_summary': ['concept_summary_', '概念_stocks_'],
        'sector_summary': ['sector_summary_', '行业_stocks_']
    }

    # 遍历所有文件，按时间戳分类
    for filename in all_files:
        if not filename.endswith('.csv'):
            continue

        timestamp = extract_timestamp_from_filename(filename)
        if not timestamp or len(timestamp) != 6:
            continue

        # 验证时间戳格式
        try:
            datetime.strptime(timestamp, '%H%M%S')
        except ValueError:
            debug_print(f"[DEBUG] 跳过无效时间戳文件: {filename}, 时间戳: {timestamp}")
            continue

        if timestamp not in file_manifest:
            file_manifest[timestamp] = {}

        # 根据文件名模式分类（按模式长度降序排序，确保更具体的模式优先匹配）
        all_patterns = []
        for file_type, patterns in file_patterns.items():
            for pattern in patterns:
                all_patterns.append((pattern, file_type))

        # 按模式长度降序排序，长模式优先匹配
        all_patterns.sort(key=lambda x: len(x[0]), reverse=True)

        # 逐一匹配模式
        for pattern, file_type in all_patterns:
            if pattern in filename:
                # 特殊处理：排除 zt_pool_previous_ 文件
                if file_type == 'limit_up' and 'zt_pool_previous_' in filename:
                    continue

                # 对于概念和行业汇总文件，需要特殊处理以包含具体名称
                if file_type in ['concept_summary', 'sector_summary']:
                        # 提取概念/行业名称作为子键
                        if file_type not in file_manifest[timestamp]:
                            file_manifest[timestamp][file_type] = {}

                        # 从文件名中提取概念/行业名称
                        if file_type == 'concept_summary':
                            if filename.startswith('concept_summary_'):
                                parts = filename.split('_')
                                if len(parts) >= 3:
                                    concept_name = parts[2]
                                    file_manifest[timestamp][file_type][concept_name] = filename
                            elif filename.startswith('概念_stocks_'):
                                parts = filename.split('_')
                                if len(parts) >= 3:
                                    concept_name = parts[2]
                                    file_manifest[timestamp][file_type][concept_name] = filename
                        elif file_type == 'sector_summary':
                            if filename.startswith('sector_summary_'):
                                parts = filename.split('_')
                                if len(parts) >= 3:
                                    sector_name = parts[2]
                                    file_manifest[timestamp][file_type][sector_name] = filename
                            elif filename.startswith('行业_stocks_'):
                                parts = filename.split('_')
                                if len(parts) >= 3:
                                    sector_name = parts[2]
                                    file_manifest[timestamp][file_type][sector_name] = filename
                else:
                    # 对于其他文件类型，直接存储文件名
                    file_manifest[timestamp][file_type] = filename
                break

    print(f"    ✅ 文件索引构建完成 - {len(file_manifest)}个时间点")

    # ==================== 方案二：初始化数据缓存池 ====================
    print("  💾 初始化数据缓存池...")
    data_cache = {}  # {filename: DataFrame}
    print("    ✅ 数据缓存池初始化完成")

    # ==================== 方案三：一次性加载数据库映射表 ====================
    print("  🗄️ 加载股票-板块映射表...")
    stock_sector_map = _load_stock_sector_mapping()
    print(f"    ✅ 股票-板块映射表加载完成 - {len(stock_sector_map)}条记录")

    return file_manifest, data_cache, stock_sector_map


def _load_stock_sector_mapping():
    """一次性加载股票-板块映射关系到内存"""
    stock_sector_map = {}

    db_path = STOCK_BLOCK_DB_PATH if os.path.exists(STOCK_BLOCK_DB_PATH) else STOCK_BLOCK_DB_FALLBACK
    if not os.path.exists(db_path):
        print(f"    ⚠️ 数据库文件不存在: {db_path}")
        return stock_sector_map

    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT stock_code, short_name, concept_blocks, industry_blocks FROM stocks")
            results = cursor.fetchall()

            for row in results:
                stock_code, short_name, concept_blocks, industry_blocks = row

                # 处理概念和行业数据
                concepts = concept_blocks.split(',') if concept_blocks else []
                industries = industry_blocks.split(',') if industry_blocks else []

                # 过滤无意义概念和行业
                filtered_concepts = filter_meaningful_concepts_and_sectors(concepts) if concepts else []
                filtered_industries = filter_meaningful_concepts_and_sectors(industries) if industries else []

                # 同时以股票代码和股票名称为键存储
                mapping_data = {
                    'concepts': filtered_concepts,
                    'industries': filtered_industries
                }

                if stock_code:
                    clean_code = str(stock_code).split('.')[0]
                    stock_sector_map[clean_code] = mapping_data

                if short_name:
                    stock_sector_map[short_name] = mapping_data

    except Exception as e:
        print(f"    ❌ 加载股票-板块映射表失败: {e}")

    return stock_sector_map


def parse_internal_data_cached(file_path, data_cache):
    """解析板块/概念内部文件，支持缓存（优化版）"""
    # 检查缓存
    if file_path in data_cache:
        return data_cache[file_path]

    # 如果缓存中没有，读取并缓存
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig')

        # 查找净流入列
        net_inflow_col = None
        if '今日主力净流入-净额' in df.columns:
            net_inflow_col = '今日主力净流入-净额'
        else:
            # 查找可能的替代列名
            possible_columns = [col for col in df.columns if '净流入' in col or 'net_inflow' in col.lower()]
            if possible_columns:
                net_inflow_col = possible_columns[0]

        if net_inflow_col is None:
            print(f"[WARNING] 文件 {file_path} 中未找到净流入列")
            data_cache[file_path] = None
            return None

        df[net_inflow_col] = df[net_inflow_col].apply(convert_to_float)
        positive_df = df[df[net_inflow_col] > 0].copy()

        # 如果使用的不是标准列名，重命名为标准列名以保持兼容性
        if net_inflow_col != '今日主力净流入-净额':
            positive_df = positive_df.rename(columns={net_inflow_col: '今日主力净流入-净额'})

        result = positive_df.sort_values(by='今日主力净流入-净额', ascending=False)
        data_cache[file_path] = result
        return result
    except Exception as e:
        print(f"[ERROR] 解析内部数据文件失败: {e}")
        data_cache[file_path] = None
        return None


def read_csv_cached(file_path, data_cache, **kwargs):
    """带缓存的CSV文件读取函数"""
    if file_path in data_cache:
        return data_cache[file_path].copy()  # 返回副本以避免意外修改

    try:
        df = pd.read_csv(file_path, **kwargs)
        data_cache[file_path] = df
        return df.copy()
    except Exception as e:
        print(f"[ERROR] 读取文件失败 {file_path}: {e}")
        data_cache[file_path] = None
        return None


# ==============================================================================
# § E. 回测主函数 (基于 backtestv6.py 改造)
# ==============================================================================

def run_analysis_backtest(date_str):
    """
    主分析函数，集成个股断层和板块内部结构分析。
    注：已移除所有买入信号功能，仅保留分析功能
    """
    # 创建log/v9目录
    log_dir = "log"
    v9_log_dir = os.path.join(log_dir, "v9")
    os.makedirs(v9_log_dir, exist_ok=True)

    # 创建日志文件路径
    log_file_path = os.path.join(v9_log_dir, f"analysis_v9_{date_str}.txt")

    # 使用日志捕获上下文管理器
    with LogCapture(log_file_path, date_str):
        print(f"--- 开始对日期 {date_str} 进行盘后复盘 (V9.0 资金流分析专用版) ---")
        data_dir = os.path.join(BASE_DATA_DIR, date_str)
        if not os.path.isdir(data_dir):
            print(f"错误: 目录不存在 {data_dir}")
            return

        # 调用实际的分析逻辑
        _run_analysis_core(date_str, data_dir, log_file_path)


def _run_analysis_core(date_str, data_dir, log_file_path):
    """分析核心逻辑（已移除所有买入功能）"""

    print(f"📊 开始资金流分析...")

    # 记录上一个时间点的涨停股池数据，用于变化对比
    previous_limit_up_stocks = []

    # ==================== 方案一：构建文件索引清单 ====================
    print("🔍 正在构建文件索引清单...")
    file_manifest, data_cache, stock_sector_map = _build_preload_data(data_dir)
    print(f"✅ 文件索引构建完成 - 共{len(file_manifest)}个时间点")

    # 1. 从文件索引中获取所有时间戳（已经优化，无需重复扫描文件）
    all_timestamps = sorted(file_manifest.keys())

    # 统计文件数量（用于调试）
    stock_flow_count = sum(1 for files in file_manifest.values() if 'stock_flow' in files)
    concept_count = sum(1 for files in file_manifest.values() if 'concept' in files)
    industry_count = sum(1 for files in file_manifest.values() if 'industry' in files)
    limit_up_count = sum(1 for files in file_manifest.values() if 'limit_up' in files)

    debug_print(f"[DEBUG] 从文件索引获取数据:")
    debug_print(f"  - 个股资金流文件: {stock_flow_count} 个时间点")
    debug_print(f"  - 概念文件: {concept_count} 个时间点")
    debug_print(f"  - 行业文件: {industry_count} 个时间点")
    debug_print(f"  - 涨停股池文件: {limit_up_count} 个时间点")
    debug_print(f"  - 总时间点数: {len(all_timestamps)}")

    if not all_timestamps:
        print("错误: 文件索引中未找到任何时间戳！")
        return

    # 2. 循环模拟每个时间点
    for ts_str in all_timestamps:
        current_sim_time_obj = datetime.strptime(ts_str, '%H%M%S').time()
        # 创建完整的datetime对象用于时间运算
        current_sim_time_full = datetime.combine(datetime.today(), current_sim_time_obj)

        # 只分析交易时间段
        if not (time(9, 30) <= current_sim_time_obj <= time(11, 30) or time(13, 0) <= current_sim_time_obj <= time(15, 0)):
            continue

        print("\n" + "=" * 25 + f" 模拟时间点: {current_sim_time_obj} " + "=" * 25)

        # 3. 从文件索引中加载当前时间点的所有数据（优化版）
        latest_rank_file = find_latest_file_from_manifest(file_manifest, 'stock_flow', current_sim_time_obj)
        latest_industry_file = find_latest_file_from_manifest(file_manifest, 'industry', current_sim_time_obj)
        latest_concept_file = find_latest_file_from_manifest(file_manifest, 'concept', current_sim_time_obj)
        latest_limit_up_file = find_latest_file_from_manifest(file_manifest, 'limit_up', current_sim_time_obj)

        # 3.1 加载涨停股池数据
        current_limit_up_stocks = []
        if latest_limit_up_file:
            current_limit_up_stocks = parse_limit_up_pool_data(os.path.join(data_dir, latest_limit_up_file))

        # 4. 先显示涨停股池分析
        if current_limit_up_stocks:
            print(f"\n--- 涨停股池 Top (模拟时间点: {current_sim_time_obj}) ---")

            # 显示涨停变化和统计信息
            # 构建热点行业统计信息（简化版）
            hot_industries_stats = {}
            if current_limit_up_stocks:
                # 统计各行业涨停数量
                industry_counts = {}
                for stock in current_limit_up_stocks:
                    industry = stock.get('所属行业', '未知')
                    if industry != '未知':
                        industry_counts[industry] = industry_counts.get(industry, 0) + 1

                # 找出涨停最多的行业作为监控行业
                if industry_counts:
                    max_count = max(industry_counts.values())
                    top_industries = [industry for industry, count in industry_counts.items() if count == max_count]
                    if top_industries:
                        hot_industries_stats['首扳最多行业'] = top_industries[0]
                        hot_industries_stats['2板连板数最多行业'] = top_industries[0]  # 简化处理

            display_limit_up_changes_and_stats(current_limit_up_stocks, previous_limit_up_stocks, stock_sector_map, hot_industries_stats)

            # 显示涨停股池表格
            stock_flow_data_for_limit_up = None
            hot_industries_info = {}  # 用于风险警示的行业资金流信息
            display_limit_up_pool_table(current_limit_up_stocks, stock_flow_data_for_limit_up, hot_industries_info, stock_sector_map)

            # 【新增：涨停池关键行业和概念内部分析】- 受SHOW_INTERNAL_ANALYSIS控制
            if SHOW_INTERNAL_ANALYSIS:
                key_industries, key_concepts = extract_limit_up_key_sectors(current_limit_up_stocks, stock_sector_map)

                if key_industries or key_concepts:
                    print(f"\n=== 涨停池热点行业/概念内部结构分析 ===")

                    # 分析关键行业
                    if key_industries:
                        print(f"\n--- 涨停池热点行业内部分析 ---")
                        for idx, industry_name in enumerate(key_industries, 1):
                            print(f"\n【涨停热点行业 {idx}】{industry_name}")
                            # 计算该行业在涨停池中的流入总量（简化估算）
                            industry_stocks = [stock for stock in current_limit_up_stocks
                                             if stock.get('所属行业', '') == industry_name]
                            estimated_inflow = len(industry_stocks) * 0.5  # 简化估算，每只涨停股0.5亿流入

                            internal_report = analyze_sector_internal_flow_wrapper(
                                industry_name, '行业', estimated_inflow, current_sim_time_obj, data_dir,
                                file_manifest=file_manifest, data_cache=data_cache)
                            if internal_report:  # 只有在有内容时才打印
                                print(internal_report)

                    # 分析关键概念
                    if key_concepts:
                        print(f"\n--- 涨停池热点概念内部分析 ---")
                        for idx, concept_name in enumerate(key_concepts, 1):
                            print(f"\n【涨停热点概念 {idx}】{concept_name}")
                            # 计算该概念在涨停池中的流入总量（简化估算）
                            concept_stocks = [stock for stock in current_limit_up_stocks
                                            if concept_name in get_stock_sectors(stock.get('名称', ''), stock_sector_map=stock_sector_map).get('concepts', [])]
                            estimated_inflow = len(concept_stocks) * 0.5  # 简化估算，每只涨停股0.5亿流入

                            internal_report = analyze_sector_internal_flow_wrapper(
                                concept_name, '概念', estimated_inflow, current_sim_time_obj, data_dir,
                                file_manifest=file_manifest, data_cache=data_cache)
                            if internal_report:  # 只有在有内容时才打印
                                print(internal_report)
                else:
                    if SHOW_INTERNAL_ANALYSIS:  # 只有在开启内部分析时才显示这个消息
                        print(f"\n=== 涨停池热点行业/概念内部结构分析 ===")
                        print("未发现具有分析价值的热点行业或概念（需要至少2只涨停股）")

        # 5. 执行个股资金流断层分析
        if latest_rank_file:
            try:
                # 使用缓存读取文件
                file_path = os.path.join(data_dir, latest_rank_file)
                df_stocks = read_csv_cached(file_path, data_cache, encoding='utf-8-sig')
                if df_stocks is None:
                    print("[ERROR] 读取个股文件失败")
                    continue
                df_stocks.columns = df_stocks.columns.str.strip().str.replace('\ufeff', '')

                # 应用列名标准化
                file_format = classify_file_type(latest_rank_file)
                debug_print(f"[DEBUG] 识别文件格式: {file_format}")
                df_stocks = standardize_stock_data(df_stocks, file_format)
                
                if df_stocks is None:
                    print("[ERROR] 列名标准化失败")
                    continue

                debug_print(f"[DEBUG] 标准化后列名: {list(df_stocks.columns)}")
                if not df_stocks.empty:
                    debug_print(f"[DEBUG] 前3行股票名称: {df_stocks['名称'].head(3).tolist()}")
                    if '今日主力净流入-净额' in df_stocks.columns:
                        debug_print(f"[DEBUG] 原始净流入数据前3行: {df_stocks['今日主力净流入-净额'].head(3).tolist()}")
                    if '最新价' in df_stocks.columns:
                        debug_print(f"[DEBUG] 原始价格数据前10行: {df_stocks[['名称', '最新价']].head(10).to_dict('records')}")
                        # 特别检查银行股的价格数据
                        bank_stocks = df_stocks[df_stocks['名称'].str.contains('银行', na=False)]
                        if not bank_stocks.empty:
                            debug_print(f"[DEBUG] 银行股价格数据: {bank_stocks[['名称', '最新价']].to_dict('records')}")
                
                # 使用convert_to_float处理monetary数据（支持中文单位"万"、"亿"）
                if '今日主力净流入-净额' in df_stocks.columns:
                    df_stocks['今日主力净流入-净额'] = df_stocks['今日主力净流入-净额'].apply(convert_to_float)
                    debug_print(f"[DEBUG] 转换后净流入数据前3行: {df_stocks['今日主力净流入-净额'].head(3).tolist()}")

                # 使用pd.to_numeric处理纯数字列
                for col in ['今日涨跌幅', '最新价']:
                    if col in df_stocks.columns:
                        debug_print(f"[DEBUG] 处理{col}列前的数据样例: {df_stocks[col].head(3).tolist()}")
                        # 特别处理最新价列，保持原值避免错误转换
                        if col == '最新价':
                            debug_print(f"[DEBUG] 价格列原始数据类型: {df_stocks[col].dtype}")
                            debug_print(f"[DEBUG] 价格列是否全为空: {df_stocks[col].isna().all()}")
                            debug_print(f"[DEBUG] 价格列非空值数量: {df_stocks[col].notna().sum()}")

                            # 检查原始数据格式
                            non_null_prices = df_stocks[col].dropna()
                            if len(non_null_prices) > 0:
                                debug_print(f"[DEBUG] 非空价格值样例: {non_null_prices.head(5).tolist()}")
                                debug_print(f"[DEBUG] 非空价格值类型: {[type(x) for x in non_null_prices.head(3)]}")

                            # 如果原始数据就是空的或无效的，跳过处理
                            if df_stocks[col].isna().all() or (df_stocks[col] == '').all():
                                debug_print(f"[DEBUG] {col}列数据为空，跳过处理")
                                continue

                            # 尝试直接转换数字
                            df_stocks[col] = pd.to_numeric(df_stocks[col], errors='coerce')
                        else:
                            df_stocks[col] = pd.to_numeric(df_stocks[col], errors='coerce')
                        debug_print(f"[DEBUG] 处理{col}列后的数据样例: {df_stocks[col].head(3).tolist()}")

                # 特别处理净占比列（可能包含%符号）
                if '今日主力净流入-净占比' in df_stocks.columns:
                    df_stocks['今日主力净流入-净占比'] = df_stocks['今日主力净流入-净占比'].apply(convert_to_float)

                # 调用个股断层分析并打印结果
                stock_gap_report = analyze_stock_flow_gap(df_stocks, current_time=current_sim_time_full, data_dir=data_dir, file_format=file_format)
                if stock_gap_report:
                    print(stock_gap_report)

                # 注：已移除个股资金流断层买入信号检测功能

                # 打印个股资金流排行榜（使用与dynamic_gap_detector.py相同的显示逻辑）
                print(f"\n--- 个股资金流入 Top 50 (模拟时间点: {current_sim_time_obj}) ---")

                # 按主力净流入排序（从大到小）
                positive_stocks = df_stocks[df_stocks['今日主力净流入-净额'] > 0].copy()
                if positive_stocks.empty:
                    print("  没有资金净流入的股票")
                    continue

                # 【修复】按净流入金额排序
                positive_stocks = positive_stocks.sort_values(by='今日主力净流入-净额', ascending=False)
                display_stocks = positive_stocks.head(50).copy()
                
                # 构建显示列和表头（与dynamic_gap_detector.py保持一致）
                display_columns = ['排名', '名称']
                headers = ['排名', '股票名称']

                # 添加排名列
                display_stocks['排名'] = range(1, len(display_stocks) + 1)

                # 【关键修复】添加股票代码列（如果存在）
                if '代码' in display_stocks.columns:
                    display_columns.append('代码')
                    headers.append('股票代码')

                # 【新增】添加行业列（如果存在）
                if '所属行业' in display_stocks.columns:
                    display_columns.append('所属行业')
                    headers.append('行业')

                # 【修复】处理涨跌幅列 - 总是显示，不管是否为0
                if '今日涨跌幅' in display_stocks.columns:
                    display_stocks['涨跌幅'] = display_stocks['今日涨跌幅'].apply(
                        lambda x: f"{x:.2f}%" if pd.notna(x) else '0.00%')
                    display_columns.append('涨跌幅')
                    headers.append('今日涨跌幅')

                # 添加主力净流入列
                display_stocks['主力净流入-净额'] = display_stocks['今日主力净流入-净额'].apply(format_amount)
                display_columns.append('主力净流入-净额')
                headers.append('今日主力净流入-净额')

                # 【修复】处理净占比列 - 确保显示
                if '今日主力净流入-净占比' in display_stocks.columns:
                    # 直接格式化显示，不过滤数据
                    display_stocks['主力净流入-净占比'] = display_stocks['今日主力净流入-净占比'].apply(
                        lambda x: f"{x:.2f}%" if pd.notna(x) and x != 0 else '0.00%')
                    display_columns.append('主力净流入-净占比')
                    headers.append('今日主力净流入-净占比')

                # 显示表格 - 调整列宽以适应新增列（排名、股票名称、代码、行业、涨跌幅、净流入、净占比）
                max_col_widths = [4, 10, 8, 12, 8, 12, 10]  # 调整列宽，增加行业列宽度
                print(tabulate(display_stocks[display_columns], headers=headers, tablefmt='psql',
                              showindex=False, maxcolwidths=max_col_widths))

            except Exception as e:
                print(f"个股资金流分析失败: {e}")

        # 注：涨停股池分析已移至前面显示

        # 保存当前涨停股池数据供下次对比（无论是否有涨停股都要更新）
        previous_limit_up_stocks = current_limit_up_stocks.copy()

        # 5. 执行板块/概念分析
        for file_type, latest_file in [('概念', latest_concept_file), ('行业', latest_industry_file)]:
            if latest_file:
                try:
                    # 使用缓存读取文件
                    file_path = os.path.join(data_dir, latest_file)
                    df_sectors = read_csv_cached(file_path, data_cache, encoding='utf-8-sig')
                    if df_sectors is None:
                        print(f"[ERROR] 读取{file_type}文件失败: {latest_file}")
                        continue
                    # 【修复】统一列名映射（参考dynamic_gap_detector.py的逻辑）
                    rename_map = {}

                    # 名称列映射
                    name_candidates = ['名称', '概念名称', '板块名称', '行业', 'name', 'concept_name']
                    if '名称' not in df_sectors.columns:
                        for candidate in name_candidates:
                            if candidate in df_sectors.columns:
                                rename_map[candidate] = '名称'
                                break

                    # 主力净流入列映射
                    inflow_candidates = [
                        '今日主力净流入-净额', '主力净流入', '净流入', '净额',
                        '今日主力净流入', '主力资金净流入', '资金净流入',
                        'main_net_inflow', 'net_inflow', 'inflow_amount'
                    ]
                    if '今日主力净流入-净额' not in df_sectors.columns:
                        for candidate in inflow_candidates:
                            if candidate in df_sectors.columns:
                                rename_map[candidate] = '今日主力净流入-净额'
                                break

                    # 应用列名映射
                    if rename_map:
                        df_sectors.rename(columns=rename_map, inplace=True)
                        debug_print(f"[DEBUG] {file_type}文件列名映射: {rename_map}")

                    # 【修复】使用convert_to_float函数正确处理数据转换（参考dynamic_gap_detector.py）
                    if '今日主力净流入-净额' in df_sectors.columns:
                        df_sectors['今日主力净流入-净额'] = df_sectors['今日主力净流入-净额'].apply(convert_to_float)

                    # 检查必要列是否存在
                    if '名称' not in df_sectors.columns or '今日主力净流入-净额' not in df_sectors.columns:
                        missing_cols = []
                        if '名称' not in df_sectors.columns:
                            missing_cols.append('名称')
                        if '今日主力净流入-净额' not in df_sectors.columns:
                            missing_cols.append('今日主力净流入-净额')
                        print(f"[WARNING] {file_type}文件缺少必要列: {missing_cols}")
                        print(f"[WARNING] 可用列: {list(df_sectors.columns)}")
                        continue

                    # 数据清理
                    df_sectors = df_sectors.dropna(subset=['名称', '今日主力净流入-净额'])

                    # 【修复】过滤正流入数据（参考dynamic_gap_detector.py）
                    df_sectors = df_sectors[df_sectors['今日主力净流入-净额'] > 0]

                    # 【新增：过滤无意义概念和行业】
                    if '名称' in df_sectors.columns:
                        df_sectors = df_sectors[df_sectors['名称'].apply(is_meaningful_concept)]
                        debug_print(f"[DEBUG] 过滤后{file_type}数量: {len(df_sectors)}")

                    # 按资金流入排序
                    df_sectors = df_sectors.sort_values(by='今日主力净流入-净额', ascending=False)

                    # 打印板块排行榜
                    print(f"\n--- {file_type}资金流入 Top 10 ---")
                    if df_sectors.empty:
                        print(f"  没有{file_type}资金净流入数据")
                        continue
                    display_sectors = df_sectors.head(10).copy()
                    display_sectors['主力净流入'] = display_sectors['今日主力净流入-净额'].apply(format_amount)
                    print(tabulate(display_sectors[['名称', '主力净流入']], headers='keys', tablefmt='psql',
                                   showindex=False))

                    # 对Top10板块进行内部结构分析（已移除买入信号检测）
                    print(f"\n=== {file_type}资金流入 Top 10 内部格局分析 ===")
                    for idx, (_, row) in enumerate(display_sectors.head(10).iterrows(), 1):
                        leader_name = row['名称']
                        total_inflow_yi = row['今日主力净流入-净额'] / 1e8

                        print(f"\n【第{idx}名】{leader_name} - 总流入: {total_inflow_yi:.2f}亿")
                        internal_report = analyze_sector_internal_flow_wrapper(
                            leader_name, file_type, total_inflow_yi, current_sim_time_obj, data_dir,
                            file_manifest=file_manifest, data_cache=data_cache)
                        if internal_report:  # 只有在有内容时才打印
                            print(internal_report)

                except Exception as e:
                    print(f"{file_type}板块分析失败: {e}")

    # ============= 分析日终总结 =============
    print("\n" + "=" * 80)
    print("📊 分析日终总结")
    print("=" * 80)

    print(f"\n📈 当日分析总结:")
    print(f"  已完成资金流分析")
    print(f"  已完成板块内部结构分析")
    print(f"  已完成涨停股池分析")
    print(f"  注：已移除所有买入信号和持仓功能")

    print(f"\n🎉 {date_str} 资金流分析完成！")
    print(f"📄 完整分析日志已保存到: {log_file_path}")


# ==============================================================================
# § E. 程序入口
# ==============================================================================

def test_concept_filter():
    """测试概念和行业过滤功能"""
    print("=== 概念和行业过滤功能测试 ===")
    
    # 测试用例
    test_concepts = [
        '人工智能', '昨日涨停', '芯片概念', '融资融券',
        '新能源汽车', '央视50', '医疗器械', 'ST板块',
        'TDX 信息', 'TDX 材料', 'tdx 消费', '养老概念'
    ]
    
    print("原始概念列表:", test_concepts)
    filtered = filter_meaningful_concepts_and_sectors(test_concepts)
    print("过滤后概念列表:", filtered)
    
    print("\n单个概念检测:")
    for concept in test_concepts:
        is_meaningful = is_meaningful_concept(concept)
        print(f"  {concept}: {'有意义' if is_meaningful else '无意义'}")
    
    print("\n无意义项目总数:", len(get_meaningless_items()))

if __name__ == "__main__":
    # 如果命令行参数包含 --test-filter，则运行过滤测试
    import sys
    if '--test-filter' in sys.argv:
        test_concept_filter()
    else:
        run_analysis_backtest(BACKTEST_DATE)